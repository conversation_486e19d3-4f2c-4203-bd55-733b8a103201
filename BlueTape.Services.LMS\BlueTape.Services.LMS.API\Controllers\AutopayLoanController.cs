using AutoMapper;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.ViewModels.AutoPay;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy.Abstractions;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace BlueTape.Services.LMS.API.Controllers;

[ApiController]
[Route(ControllersConstants.AutopayLoan)]
[Authorize]
public class AutopayLoanController
{
    private readonly IMapper _mapper;
    private readonly IAutoPayLoanService _autoPayLoanService;
    private readonly IEnumerable<IGetDueStrategy> _getDueStrategies;
    private readonly IEnumerable<IProcessDueStrategy> _processDueStrategies;

    public AutopayLoanController(IAutoPayLoanService autoPayLoanService, IMapper mapper, IEnumerable<IGetDueStrategy> getDueStrategies, IEnumerable<IProcessDueStrategy> processDueStrategies)
    {
        _mapper = mapper;
        _getDueStrategies = getDueStrategies;
        _autoPayLoanService = autoPayLoanService;
        _processDueStrategies = processDueStrategies;
    }

    /// <summary>
    /// Get loan for autopay
    /// </summary>
    /// <param name="autopayLoanQuery"></param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /AutopayLoans?UpcomingDate=2022-10-20&amp;CountDaysForUpcomming=3,
    ///     GET /AutopayLoans
    ///     
    /// </remarks>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<AutoPayLoanDto>> GetUpcoming([FromQuery] AutopayLoanQuery autopayLoanQuery, CancellationToken ct)
    {
        var date = (autopayLoanQuery.UpcomingDate.HasValue) ? autopayLoanQuery.UpcomingDate.Value : DateOnly.FromDateTime(DateTime.UtcNow);
        var countDays = autopayLoanQuery.CountDaysForUpcoming;
        var product = _mapper.Map<Domain.Enums.ProductType>(autopayLoanQuery.ProductType);
        var result = await _autoPayLoanService.GetUpcoming(date, countDays, ct, product, autopayLoanQuery.CompanyId);
        return _mapper.Map<IEnumerable<AutoPayLoanDto>>(result);
    }

    [HttpGet("getDue")]
    public async Task<List<DueLoanMessageViewModel>> GetDue([FromQuery] ProductType productType, CancellationToken ct)
    {
        var product = _mapper.Map<Domain.Enums.ProductType>(productType);
        var applicableStrategy = _getDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
        return _mapper.Map<List<DueLoanMessageViewModel>>(await applicableStrategy.GetDueLoans(ct));
    }

    [HttpGet("getDue/{companyId}")]
    public async Task<List<DueLoanMessageViewModel>> GetDue([FromQuery] ProductType productType, [FromQuery] string companyId, CancellationToken ct)
    {
        var product = _mapper.Map<Domain.Enums.ProductType>(productType);
        var applicableStrategy = _getDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
        return _mapper.Map<List<DueLoanMessageViewModel>>(await applicableStrategy.GetDueLoans(ct, companyId));
    }


    [HttpPost("processDue")]
    public async Task ProcessDue([FromBody] string jsonData, CancellationToken ct)
    {
        try
        {
            var settings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            // Parse the raw JSON to a dynamic object first to handle property name mapping
            var jsonObject = JsonConvert.DeserializeObject<dynamic>(jsonData, settings);

            if (jsonObject == null)
            {
                throw new BadHttpRequestException("Invalid JSON: Unable to parse the provided data");
            }

            // Create DueLoanMessage manually to handle the property mapping
            var message = new DueLoanMessage
            {
                Event = jsonObject["event"]?.ToString() ?? string.Empty,
                BlueTapeCorrelationId = jsonObject["blueTapeCorrelationId"]?.ToString() ?? string.Empty,
                CreatedAt = DateTime.Parse(jsonObject["createdAt"]?.ToString() ?? DateTime.UtcNow.ToString()),
                CreatedBy = jsonObject["createdBy"]?.ToString() ?? string.Empty,
                CompanyId = jsonObject["companyId"]?.ToString() ?? string.Empty,
                DueSum = decimal.Parse(jsonObject["dueSum"]?.ToString() ?? "0"),
                DueCount = int.Parse(jsonObject["dueCount"]?.ToString() ?? "0"),
                ProductType = Enum.Parse<Domain.Enums.ProductType>(jsonObject["productType"]?.ToString() ?? "InHouseCredit"),
                Loans = new List<DueLoanItem>()
            };

            // Parse the loans array
            if (jsonObject["loans"] != null)
            {
                foreach (var loanJson in jsonObject["loans"])
                {
                    var loan = new DueLoanItem
                    {
                        LoanId = Guid.Parse(loanJson["loanId"]?.ToString() ?? Guid.Empty.ToString()),
                        PayableIds = JsonConvert.DeserializeObject<List<string>>(loanJson["payableIds"]?.ToString() ?? "[]") ?? new List<string>(),
                        CompanyId = loanJson["companyId"]?.ToString() ?? string.Empty,
                        NextPaymentAmount = decimal.Parse(loanJson["nextPaymentAmount"]?.ToString() ?? "0"),
                        OverDueAmount = decimal.Parse(loanJson["overDueAmount"]?.ToString() ?? "0"),
                        NextPaymentDate = DateOnly.Parse(loanJson["nextPaymentDate"]?.ToString() ?? DateOnly.FromDateTime(DateTime.UtcNow).ToString()),
                        IsOverdue = bool.Parse(loanJson["isOverdue"]?.ToString() ?? "false"),
                        DueStatus = loanJson["dueStatus"]?.ToString() ?? string.Empty
                    };
                    message.Loans.Add(loan);
                }
            }

            var product = message.ProductType;
            var applicableStrategy = _processDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
            await applicableStrategy.ProcessDue(message, ct);
        }
        catch (JsonException ex)
        {
            throw new BadHttpRequestException($"Invalid JSON format: {ex.Message}");
        }
        catch (Exception ex)
        {
            throw new BadHttpRequestException($"Error processing due loan data: {ex.Message}");
        }
    }
}
