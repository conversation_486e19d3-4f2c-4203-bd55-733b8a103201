using AutoMapper;
using BlueTape.LS.Domain.Enums;
using BlueTape.LS.DTOs.Loan;
using BlueTape.Services.LMS.API.Constants;
using BlueTape.Services.LMS.API.ViewModels.AutoPay;
using BlueTape.Services.LMS.Application.Models.AutoPay;
using BlueTape.Services.LMS.AutoPay.Abstractions.Services;
using BlueTape.Services.LMS.AutoPay.Infrastructure.GetDueStrategy.Abstractions;
using BlueTape.Services.LMS.AutoPay.Infrastructure.ProcessDueStrategy.Abstractions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace BlueTape.Services.LMS.API.Controllers;

[ApiController]
[Route(ControllersConstants.AutopayLoan)]
[Authorize]
public class AutopayLoanController
{
    private readonly IMapper _mapper;
    private readonly IAutoPayLoanService _autoPayLoanService;
    private readonly IEnumerable<IGetDueStrategy> _getDueStrategies;
    private readonly IEnumerable<IProcessDueStrategy> _processDueStrategies;

    public AutopayLoanController(IAutoPayLoanService autoPayLoanService, IMapper mapper, IEnumerable<IGetDueStrategy> getDueStrategies, IEnumerable<IProcessDueStrategy> processDueStrategies)
    {
        _mapper = mapper;
        _getDueStrategies = getDueStrategies;
        _autoPayLoanService = autoPayLoanService;
        _processDueStrategies = processDueStrategies;
    }

    /// <summary>
    /// Get loan for autopay
    /// </summary>
    /// <param name="autopayLoanQuery"></param>
    /// <remarks>
    /// Sample request:
    /// 
    ///     GET /AutopayLoans?UpcomingDate=2022-10-20&amp;CountDaysForUpcomming=3,
    ///     GET /AutopayLoans
    ///     
    /// </remarks>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<AutoPayLoanDto>> GetUpcoming([FromQuery] AutopayLoanQuery autopayLoanQuery, CancellationToken ct)
    {
        var date = (autopayLoanQuery.UpcomingDate.HasValue) ? autopayLoanQuery.UpcomingDate.Value : DateOnly.FromDateTime(DateTime.UtcNow);
        var countDays = autopayLoanQuery.CountDaysForUpcoming;
        var product = _mapper.Map<Domain.Enums.ProductType>(autopayLoanQuery.ProductType);
        var result = await _autoPayLoanService.GetUpcoming(date, countDays, ct, product, autopayLoanQuery.CompanyId);
        return _mapper.Map<IEnumerable<AutoPayLoanDto>>(result);
    }

    [HttpGet("getDue")]
    public async Task<List<DueLoanMessageViewModel>> GetDue([FromQuery] ProductType productType, CancellationToken ct)
    {
        var product = _mapper.Map<Domain.Enums.ProductType>(productType);
        var applicableStrategy = _getDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
        return _mapper.Map<List<DueLoanMessageViewModel>>(await applicableStrategy.GetDueLoans(ct));
    }

    [HttpGet("getDue/{companyId}")]
    public async Task<List<DueLoanMessageViewModel>> GetDue([FromQuery] ProductType productType, [FromQuery] string companyId, CancellationToken ct)
    {
        var product = _mapper.Map<Domain.Enums.ProductType>(productType);
        var applicableStrategy = _getDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
        return _mapper.Map<List<DueLoanMessageViewModel>>(await applicableStrategy.GetDueLoans(ct, companyId));
    }


    [HttpPost("processDue")]
    public async Task ProcessDue([FromBody] string jsonData, CancellationToken ct)
    {
        try
        {
            var settings = new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore,
                //MissingMemberHandling = MissingMemberHandling.Error,
                DateTimeZoneHandling = DateTimeZoneHandling.Utc,
                ContractResolver = new DefaultContractResolver
                {
                    NamingStrategy = new DefaultNamingStrategy
                    {
                        OverrideSpecifiedNames = true // Ignores [JsonProperty] names
                    }
                }
            };

            // Parse the JSON array to List<DueLoanMessage>
            var message = JsonConvert.DeserializeObject<DueLoanMessage>(jsonData);


            var product = _mapper.Map<Domain.Enums.ProductType>(message.ProductType);
            var applicableStrategy = _processDueStrategies.FirstOrDefault(x => x.IsApplicable(product))!;
            await applicableStrategy.ProcessDue(message, ct);
        }
        catch (JsonException ex)
        {
            throw new BadHttpRequestException($"Invalid JSON format: {ex.Message}");
        }
    }
}
